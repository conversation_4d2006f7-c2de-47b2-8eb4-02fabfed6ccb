import time
import threading
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import re

def setup_browser():
    """Setup a headless Chrome browser with human-like settings"""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        
        # Disable images and CSS for faster loading
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        return driver
    except Exception as e:
        print(f"⚠️ Browser setup error: {e}")
        return None

def human_like_click_get_here(download_url):
    """Simulate human clicking GET HERE button using real browser"""
    driver = None
    try:
        print(f"🤖 Starting browser to simulate human click...")
        driver = setup_browser()
        if not driver:
            return None
        
        print(f"🔄 Loading page: {download_url}")
        driver.get(download_url)
        
        # Wait for page to load
        time.sleep(3)
        
        print(f"📄 Page loaded, looking for GET HERE button...")
        
        # Look for GET HERE button with various selectors
        get_here_selectors = [
            "//a[contains(text(), 'GET HERE')]",
            "//button[contains(text(), 'GET HERE')]",
            "//div[contains(text(), 'GET HERE')]",
            "//span[contains(text(), 'GET HERE')]",
            "//*[contains(@class, 'get-here')]",
            "//*[contains(@id, 'get-here')]"
        ]
        
        button_found = False
        for selector in get_here_selectors:
            try:
                print(f"🔍 Looking for GET HERE button with selector: {selector}")
                button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                
                print(f"✅ Found GET HERE button!")
                
                # Scroll to button (human-like behavior)
                driver.execute_script("arguments[0].scrollIntoView(true);", button)
                time.sleep(1)
                
                # Get current URL before click
                current_url = driver.current_url
                print(f"📍 Current URL: {current_url}")
                
                # Click the button
                print(f"👆 Clicking GET HERE button...")
                button.click()
                
                # Wait for redirect/navigation
                print(f"⏳ Waiting for redirect...")
                time.sleep(5)
                
                # Check if URL changed (redirect happened)
                new_url = driver.current_url
                print(f"📍 New URL after click: {new_url}")
                
                if new_url != current_url and 'canva.com' in new_url:
                    print(f"🎯 Success! Redirected to Canva: {new_url}")
                    return new_url
                
                # If no redirect, check if page content changed
                page_source = driver.page_source
                canva_patterns = [
                    r'(https://www\.canva\.com/signup\?[^"\s]+)',
                    r'(https://www\.canva\.com/brand/join\?[^"\s]+)',
                    r'window\.location\.href\s*=\s*["\']([^"\']*canva[^"\']*)["\']'
                ]
                
                for pattern in canva_patterns:
                    matches = re.findall(pattern, page_source)
                    for match in matches:
                        if 'canva.com' in match:
                            print(f"🎯 Found Canva link in updated page: {match}")
                            return match
                
                button_found = True
                break
                
            except (TimeoutException, NoSuchElementException):
                continue
        
        if not button_found:
            print("⚠️ GET HERE button not found with any selector")
            
            # Show page content for debugging
            print("🔍 Page content (first 1000 chars):")
            print(driver.page_source[:1000])
        
        return None
        
    except Exception as e:
        print(f"⚠️ Browser automation error: {e}")
        return None
    finally:
        if driver:
            try:
                driver.quit()
                print("🔒 Browser closed")
            except:
                pass

def threaded_test(download_url, result_container):
    """Run browser test in a separate thread"""
    try:
        print(f"🧵 Starting threaded browser test...")
        result = human_like_click_get_here(download_url)
        result_container['link'] = result
        result_container['success'] = True
        print(f"🧵 Thread completed successfully")
    except Exception as e:
        print(f"🧵 Thread error: {e}")
        result_container['link'] = None
        result_container['success'] = False

# Test the browser automation
download_url = "https://biozium.com/public/bio-links/kane-tanner/"

print("=== Testing Browser Automation ===")
print(f"Target URL: {download_url}")

# Test with threading
result_container = {'link': None, 'success': False}
browser_thread = threading.Thread(
    target=threaded_test, 
    args=(download_url, result_container)
)

print("🧵 Starting threaded browser automation...")
browser_thread.start()
browser_thread.join(timeout=90)  # Wait max 90 seconds

if browser_thread.is_alive():
    print("⚠️ Browser automation timed out")
elif result_container['success'] and result_container['link']:
    canva_link = result_container['link']
    print(f"✅ SUCCESS! Found Canva link: {canva_link}")
    
    # Compare with expected manual result
    manual_link = "https://www.canva.com/signup/?brandAccessToken=uVfLa2sYKlBgWmKHEN0nAA&invitationToken=uVfLa2sYKlBgWmKHEN0nAA&brandingVariant=edu&signupRedirect=%2Fbrand%2Fjoin%3Ftoken%3DuVfLa2sYKlBgWmKHEN0nAA%26brandingVariant%3Dedu%26referrer%3Dteam-invite&loginRedirect=%2Fbrand%2Fjoin%3Ftoken%3DuVfLa2sYKlBgWmKHEN0nAA%26brandingVariant%3Dedu%26referrer%3Dteam-invite"
    
    print(f"\n🔍 Comparison:")
    print(f"Browser found: {canva_link}")
    print(f"Manual result: {manual_link}")
    
    if canva_link == manual_link:
        print("🎉 Perfect match! Browser automation is working correctly!")
    else:
        print("🔍 Different links - checking tokens...")
        
        # Extract tokens for comparison
        browser_token = re.search(r'(?:brandAccessToken|token)=([^&]+)', canva_link)
        manual_token = re.search(r'brandAccessToken=([^&]+)', manual_link)
        
        if browser_token and manual_token:
            print(f"Browser token: {browser_token.group(1)}")
            print(f"Manual token: {manual_token.group(1)}")
            
            if browser_token.group(1) != manual_token.group(1):
                print("🎯 Different tokens detected - this means the link has been updated!")
else:
    print("❌ Browser automation failed")
