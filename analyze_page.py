import requests
import re

url = 'https://biozium.com/public/bio-links/kane-tanner/'
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

try:
    response = requests.get(url, headers=headers, timeout=15)
    print('Status Code:', response.status_code)
    print('Content Length:', len(response.text))
    print('\n--- Searching for Canva links ---')

    # Search for various Canva link patterns
    patterns = [
        r'const link = "(https://www\.canva\.com/brand/join\?[^"]+)"',
        r'href="(https://www\.canva\.com/brand/join\?[^"]+)"',
        r'(https://www\.canva\.com/brand/join\?[^"\'>\s]+)',
        r'"(https://www\.canva\.com[^"]+)"'
    ]

    for i, pattern in enumerate(patterns, 1):
        matches = re.findall(pattern, response.text)
        print(f'Pattern {i}: {len(matches)} matches')
        for match in matches[:3]:  # Show first 3 matches
            print(f'  - {match}')

    print('\n--- Looking for GET HERE button ---')
    # Look for GET HERE button structure
    get_here_pattern = r'<a[^>]*href="([^"]+)"[^>]*>.*?GET HERE.*?</a>'
    get_here_matches = re.findall(get_here_pattern, response.text, re.IGNORECASE | re.DOTALL)
    print(f'GET HERE links found: {len(get_here_matches)}')
    for match in get_here_matches:
        print(f'  - {match}')

    print('\n--- Raw content sample (first 2000 chars) ---')
    print(response.text[:2000])

except Exception as e:
    print(f'Error: {e}')
