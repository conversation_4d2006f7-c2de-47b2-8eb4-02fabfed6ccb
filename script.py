import requests
import os
import time
import re
import sys
import json
import threading
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

# CONFIGURATION
PLATFORM_URL = "https://bingotingo.com/best-social-media-platforms/"  # Main platform page
LINK_FILE = "last_canva_link.txt"
TOKEN_FILE = "last_canva_token.txt"
CHECK_INTERVAL = 1800  # 30 minutes in seconds

# TELEGRAM SETUP - PRIVATE CHANNEL
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHANNEL_ID = "-1001427877073"  # Your private channel ID

def verify_bot_access():
    """Verify if bot has access to the channel"""
    try:
        test_url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getChat"
        response = requests.post(test_url, json={"chat_id": TELEGRAM_CHANNEL_ID})
        data = response.json()
        
        if response.status_code == 200 and data.get("ok"):
            print("✅ Bot has access to the channel")
            return True
        else:
            print(f"❌ Bot access error: {data.get('description', 'Unknown error')}")
            return False
    except Exception as e:
        print(f"⚠️ Verification failed: {e}")
        return False

def extract_token_from_link(link):
    """Extract the main token from a Canva link"""
    if not link:
        return None

    # Try different token patterns
    token_patterns = [
        r'brandAccessToken=([^&]+)',  # Signup URLs
        r'invitationToken=([^&]+)',   # Signup URLs
        r'token=([^&]+)'              # Brand/join URLs
    ]

    for pattern in token_patterns:
        match = re.search(pattern, link)
        if match:
            return match.group(1)

    return None

def is_link_actually_new(current_link, stored_link):
    """Check if the current link is actually different from stored link"""
    if not stored_link:
        return True  # No stored link, so it's new

    if current_link == stored_link:
        return False  # Exact same link

    # Compare tokens - if tokens are different, it's a new link
    current_token = extract_token_from_link(current_link)
    stored_token = extract_token_from_link(stored_link)

    if current_token and stored_token:
        if current_token != stored_token:
            print(f"🔄 Token changed: {stored_token} → {current_token}")
            return True
        else:
            print(f"✅ Same token: {current_token}")
            return False

    # If we can't extract tokens, compare URLs
    return current_link != stored_link

def setup_browser():
    """Setup a headless Chrome browser with human-like settings"""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Disable images and CSS for faster loading
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # Use webdriver-manager to automatically handle ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # Execute script to remove webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        driver.set_page_load_timeout(30)
        return driver
    except Exception as e:
        print(f"⚠️ Browser setup error: {e}")
        return None

def human_like_click_get_here(download_url):
    """Simulate human clicking GET HERE button using real browser"""
    driver = None
    try:
        print(f"🤖 Starting browser to simulate human click...")
        driver = setup_browser()
        if not driver:
            return None

        print(f"🔄 Loading page: {download_url}")
        driver.get(download_url)

        # Wait for page to load completely
        time.sleep(3)

        # Wait for any JavaScript to execute and update links
        print(f"⏳ Waiting for JavaScript to load and update links...")
        time.sleep(5)

        # Look for GET HERE button with various selectors
        get_here_selectors = [
            "//a[contains(text(), 'GET HERE')]",
            "//button[contains(text(), 'GET HERE')]",
            "//div[contains(text(), 'GET HERE')]",
            "//span[contains(text(), 'GET HERE')]",
            "//*[contains(@class, 'get-here')]",
            "//*[contains(@id, 'get-here')]"
        ]

        button_found = False
        for selector in get_here_selectors:
            try:
                print(f"🔍 Looking for GET HERE button with selector: {selector}")
                button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )

                print(f"✅ Found GET HERE button!")

                # Scroll to button (human-like behavior)
                driver.execute_script("arguments[0].scrollIntoView(true);", button)
                time.sleep(1)

                # Get current URL before click
                current_url = driver.current_url
                print(f"📍 Current URL: {current_url}")

                # Get initial page content to compare
                initial_content = driver.page_source
                initial_canva_links = re.findall(r'(https://www\.canva\.com/[^"\s]+)', initial_content)
                print(f"🔍 Initial Canva links found: {len(initial_canva_links)}")

                # Click the button
                print(f"👆 Clicking GET HERE button...")
                button.click()

                # Wait and monitor for changes
                print(f"⏳ Monitoring for link updates...")
                for attempt in range(10):  # Check for 10 seconds
                    time.sleep(1)

                    # Check if URL changed (direct redirect)
                    new_url = driver.current_url
                    if new_url != current_url and 'canva.com' in new_url:
                        print(f"🎯 Success! Redirected to Canva: {new_url}")
                        return new_url

                    # Check if page content updated with new links
                    current_content = driver.page_source
                    canva_patterns = [
                        r'(https://www\.canva\.com/signup\?[^"\s]+)',
                        r'(https://www\.canva\.com/brand/join\?[^"\s]+)',
                        r'window\.location\.href\s*=\s*["\']([^"\']*canva[^"\']*)["\']'
                    ]

                    for pattern in canva_patterns:
                        matches = re.findall(pattern, current_content)
                        for match in matches:
                            if 'canva.com' in match:
                                # Check if this is a new/different link
                                if match not in initial_canva_links:
                                    print(f"🎯 Found NEW Canva link after click: {match}")
                                    return match
                                elif '/signup?' in match:  # Prefer signup URLs even if not new
                                    print(f"🎯 Found signup Canva link: {match}")
                                    return match

                    print(f"⏳ Attempt {attempt + 1}/10 - still monitoring...")

                # If no new link found, return the best available link
                final_content = driver.page_source
                for pattern in canva_patterns:
                    matches = re.findall(pattern, final_content)
                    for match in matches:
                        if 'canva.com' in match:
                            print(f"🔍 Returning best available link: {match}")
                            return match

                button_found = True
                break

            except (TimeoutException, NoSuchElementException):
                continue

        if not button_found:
            print("⚠️ GET HERE button not found with any selector")

            # Fallback: look for any clickable elements that might be the button
            print("🔍 Looking for alternative clickable elements...")
            clickable_elements = driver.find_elements(By.XPATH, "//a | //button | //div[@onclick] | //span[@onclick]")

            for element in clickable_elements:
                try:
                    text = element.text.strip().upper()
                    if 'GET' in text and 'HERE' in text:
                        print(f"🔍 Found potential GET HERE element: {text}")
                        element.click()
                        time.sleep(3)

                        new_url = driver.current_url
                        if 'canva.com' in new_url:
                            print(f"🎯 Success with alternative element: {new_url}")
                            return new_url
                except:
                    continue

        return None

    except Exception as e:
        print(f"⚠️ Browser automation error: {e}")
        return None
    finally:
        if driver:
            try:
                driver.quit()
                print("🔒 Browser closed")
            except:
                pass

def get_download_link():
    """Get the current download link from the platform page using multiple methods"""
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

        print("🔄 Loading platform page...")
        response = requests.get(PLATFORM_URL, headers=headers, timeout=15)
        response.raise_for_status()

        # Method 1: Look for direct biozium links
        patterns = [
            r'href="(https://biozium\.com/public/bio-links/[^"]+)"',
            r'href="(https://[^"]*biozium[^"]*bio-links[^"]*)"',
            r'"(https://biozium\.com/public/bio-links/[^"]+)"',
            r'href="([^"]*bio-links/[^"]*)"'
        ]

        for i, pattern in enumerate(patterns, 1):
            matches = re.findall(pattern, response.text)
            if matches:
                # Take the first match that looks valid
                for match in matches:
                    if 'biozium.com' in match and 'bio-links' in match:
                        print(f"� Found download link (method 1, pattern {i}): {match}")
                        return match

        # Method 2: Look for any download-related links and try to follow redirects
        print("🔍 Method 1 failed, trying method 2...")
        download_patterns = [
            r'href="([^"]*)" [^>]*download[^>]*',
            r'href="([^"]*)"[^>]*>.*?[Dd]ownload.*?</a>',
            r'<a[^>]*href="([^"]*)"[^>]*>.*?[Dd]ownload.*?</a>'
        ]

        for pattern in download_patterns:
            matches = re.findall(pattern, response.text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if match.startswith('http'):
                    print(f"🔄 Following potential download link: {match}")
                    try:
                        # Follow the redirect to see where it goes
                        redirect_response = requests.head(match, headers=headers, allow_redirects=True, timeout=10)
                        final_url = redirect_response.url
                        if 'biozium.com' in final_url and 'bio-links' in final_url:
                            print(f"📥 Found download link (method 2): {final_url}")
                            return final_url
                    except:
                        continue

        print("⚠️ Could not find download link using any method")
        return None

    except Exception as e:
        print(f"⚠️ Platform page error: {e}")
        return None

def simulate_click_get_here(download_url):
    """Simulate clicking the GET HERE button to get the actual redirect URL"""
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Referer": download_url
        }

        print(f"🔄 Loading download page to find GET HERE button: {download_url}")
        response = requests.get(download_url, headers=headers, timeout=15)
        response.raise_for_status()

        # Look for GET HERE button patterns
        get_here_patterns = [
            r'<a[^>]*href="([^"]*)"[^>]*>.*?GET HERE.*?</a>',
            r'href="([^"]*)"[^>]*>.*?GET HERE',
            r'onclick="[^"]*location\.href\s*=\s*[\'"]([^\'"]*)[\'"]\s*"[^>]*>.*?GET HERE',
            r'onclick="[^"]*window\.open\([\'"]([^\'"]*)[\'"]\)[^>]*>.*?GET HERE'
        ]

        for i, pattern in enumerate(get_here_patterns, 1):
            matches = re.findall(pattern, response.text, re.IGNORECASE | re.DOTALL)
            if matches:
                for match in matches:
                    if match and match != '#':
                        print(f"🔗 Found GET HERE link (pattern {i}): {match}")

                        # If it's a relative URL, make it absolute
                        if match.startswith('/'):
                            from urllib.parse import urljoin
                            match = urljoin(download_url, match)

                        # Follow the GET HERE link
                        if match.startswith('http'):
                            print(f"🔄 Following GET HERE redirect: {match}")
                            try:
                                redirect_response = requests.get(match, headers=headers, allow_redirects=True, timeout=15)
                                final_url = redirect_response.url
                                print(f"🎯 GET HERE redirects to: {final_url}")

                                # Check if this is a Canva URL
                                if 'canva.com' in final_url:
                                    return final_url

                            except Exception as e:
                                print(f"⚠️ Error following GET HERE link: {e}")
                                continue

        return None
    except Exception as e:
        print(f"⚠️ Error simulating GET HERE click: {e}")
        return None

def extract_link():
    """Extract Canva Pro invite link using human-like browser automation"""
    try:
        # Step 1: Get the current download link from platform
        download_url = get_download_link()
        if not download_url:
            return None

        # Step 2: Try human-like browser automation first (most accurate) - with threading
        print(f"🤖 Method 1: Human-like browser automation (threaded)")

        # Use threading to prevent browser from blocking the main process
        result_container = {'link': None, 'success': False}
        browser_thread = threading.Thread(
            target=threaded_link_extraction,
            args=(download_url, result_container)
        )

        browser_thread.start()
        browser_thread.join(timeout=60)  # Wait max 60 seconds for browser

        if browser_thread.is_alive():
            print("⚠️ Browser automation timed out, continuing with fallback...")
        elif result_container['success'] and result_container['link']:
            canva_link = result_container['link']
            if 'canva.com' in canva_link:
                print(f"🎯 Success with threaded browser automation: {canva_link}")
                return canva_link

        # Step 3: Fallback to parsing page content (if browser fails)
        print(f"🔄 Method 2: Fallback to page content parsing")
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }

        # Add timestamp to bypass cache
        timestamp = int(time.time())
        fresh_url = f"{download_url}?t={timestamp}"

        print(f"🔄 Loading fresh download page: {fresh_url}")
        response = requests.get(fresh_url, headers=headers, timeout=15)
        response.raise_for_status()

        # Find ALL Canva links on the page
        all_canva_patterns = [
            r'const link = "(https://www\.canva\.com/[^"]+)"',
            r'"(https://www\.canva\.com/signup\?[^"]+)"',
            r'"(https://www\.canva\.com/brand/join\?[^"]+)"',
            r'href="(https://www\.canva\.com/[^"]+)"',
            r'(https://www\.canva\.com/signup\?[^"\s]+)',
            r'(https://www\.canva\.com/brand/join\?[^"\s]+)',
            r'window\.location\.href\s*=\s*["\']([^"\']*canva[^"\']*)["\']',
            r'location\.href\s*=\s*["\']([^"\']*canva[^"\']*)["\']'
        ]

        found_links = []

        for i, pattern in enumerate(all_canva_patterns, 1):
            matches = re.findall(pattern, response.text)
            for match in matches:
                if 'canva.com' in match and match not in found_links:
                    found_links.append(match)
                    print(f"🔍 Found Canva link (pattern {i}): {match}")

        if not found_links:
            print("⚠️ No Canva links found with fallback method")
            return None

        # Analyze links and prefer the most recent/complete one
        best_link = None
        best_score = 0

        for link in found_links:
            score = 0

            # Prefer signup URLs (they're typically the fresh ones)
            if '/signup?' in link:
                score += 100
                print(f"🎯 Signup URL found (preferred): {link}")
            elif '/brand/join?' in link:
                score += 50
                print(f"🔍 Brand/join URL found: {link}")

            # Prefer links with brandAccessToken (newer format)
            if 'brandAccessToken=' in link:
                score += 50
                print(f"🎯 Has brandAccessToken (newer): {link}")

            # Prefer links with invitationToken
            if 'invitationToken=' in link:
                score += 25
                print(f"🎯 Has invitationToken: {link}")

            # Prefer longer URLs (more parameters = more complete)
            score += len(link) // 10

            print(f"📊 Link score: {score} - {link[:80]}...")

            if score > best_score:
                best_score = score
                best_link = link

        if best_link:
            print(f"🎯 Selected best fallback link (score {best_score}): {best_link}")
            return best_link
        else:
            # Final fallback to first link
            best_link = found_links[0]
            print(f"🎯 Final fallback to first link: {best_link}")
            return best_link

    except Exception as e:
        print(f"⚠️ Extraction error: {e}")
        return None

def threaded_link_extraction(download_url, result_container):
    """Run link extraction in a separate thread"""
    try:
        print(f"🧵 Starting threaded browser automation...")
        result = human_like_click_get_here(download_url)
        result_container['link'] = result
        result_container['success'] = True
        print(f"🧵 Thread completed successfully")
    except Exception as e:
        print(f"🧵 Thread error: {e}")
        result_container['link'] = None
        result_container['success'] = False

def send_to_channel(message, link=None):
    """Send message to Telegram channel with button and return status"""
    try:
        # Create inline keyboard if link is provided
        reply_markup = None
        if link:
            reply_markup = {
                "inline_keyboard": [
                    [{"text": "🎁 Claim Canva Pro Now", "url": link}]
                ]
            }
        
        # Send message to channel
        response = requests.post(
            f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage",
            json={
                "chat_id": TELEGRAM_CHANNEL_ID,
                "text": message,
                "parse_mode": "Markdown",
                "reply_markup": reply_markup
            },
            timeout=10
        )
        
        # Check response
        if response.status_code == 200:
            print("✅ Message sent successfully")
            return True
        else:
            error_msg = response.json().get('description', 'Unknown error')
            print(f"❌ Telegram API error: {error_msg}")
            return False
    except Exception as e:
        print(f"⚠️ Telegram error: {e}")
        return False

def main():
    print(f"🚀 Starting Canva Pro Monitor for your private channel")
    print(f"🔗 Monitoring: {PLATFORM_URL}")
    print(f"🔔 Updates will be sent to Channel ID: {TELEGRAM_CHANNEL_ID}")
    print(f"⏱ Checking every {CHECK_INTERVAL//60} minutes\n")
    
    # Verify bot access before proceeding
    if not verify_bot_access():
        print("\n❌ CRITICAL: Bot doesn't have access to the channel")
        print("Please make sure:")
        print("1. Bot @myProCanvaBot is added as admin to your channel")
        print("2. Bot has 'Post Messages' permission")
        print("3. Channel ID is correct")
        print("\nExiting...")
        return
    
    # First-run notification
    first_run = not os.path.exists(LINK_FILE)
    if first_run:
        print("🌟 First run detected")
        if send_to_channel("🔔 *Canva Pro Link Monitor Activated!*\n"
                          "I'll automatically post new invite links here."):
            print("✅ Channel activation message sent")
        else:
            print("❌ Failed to send activation message")
    
    while True:
        current_time = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"\n[{current_time}] Checking for updates...")
        
        current_link = extract_link()
        
        if current_link:
            print(f"🔗 Extracted link: {current_link}")
            
            # Check if link is new
            if first_run or not os.path.exists(LINK_FILE):
                # First link detected
                with open(LINK_FILE, "w") as f:
                    f.write(current_link)
                if send_to_channel(
                    f"🚀 *First Canva Pro Invite Detected!*\n\n"
                    f"⏰ Detected: {current_time}\n\n"
                    "_Click below to claim your access:_",
                    current_link
                ):
                    print("✅ Initial link sent to channel")
                    first_run = False
                else:
                    print("❌ Failed to send initial link")
                
            else:
                # Check if link has actually changed (using token comparison)
                with open(LINK_FILE, "r") as f:
                    old_link = f.read().strip()

                if is_link_actually_new(current_link, old_link):
                    # Update stored link
                    with open(LINK_FILE, "w") as f:
                        f.write(current_link)

                    # Extract and display token info
                    current_token = extract_token_from_link(current_link)
                    old_token = extract_token_from_link(old_link)

                    print(f"🔄 Link changed!")
                    print(f"   Old token: {old_token}")
                    print(f"   New token: {current_token}")

                    # Send to channel
                    if send_to_channel(
                        f"🔄 *New Canva Pro Invite Available!*\n\n"
                        f"⏰ Detected: {current_time}\n"
                        f"🔑 Token: `{current_token}`\n\n"
                        "_Click below to claim your access:_",
                        current_link
                    ):
                        print("✅ New link sent to channel")
                    else:
                        print("⚠️ Failed to send new link to channel")
                else:
                    print("✅ Link unchanged (same token)")
        else:
            print("⚠️ Could not extract link")
        
        # Wait for next check
        print(f"⏳ Next check in {CHECK_INTERVAL//60} minutes...")
        time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    # Run continuously with restart on error
    while True:
        try:
            main()
        except Exception as e:
            print(f"🛑 Critical error: {e}")
            print("🔄 Restarting in 60 seconds...")
            time.sleep(60)