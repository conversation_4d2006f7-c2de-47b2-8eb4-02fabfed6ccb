import requests
import os
import time
import re
import sys

# CONFIGURATION
PLATFORM_URL = "https://bingotingo.com/best-social-media-platforms/"  # Main platform page
LINK_FILE = "last_canva_link.txt"
CHECK_INTERVAL = 1800  # 30 minutes in seconds

# TELEGRAM SETUP - PRIVATE CHANNEL
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHANNEL_ID = "-1001427877073"  # Your private channel ID

def verify_bot_access():
    """Verify if bot has access to the channel"""
    try:
        test_url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getChat"
        response = requests.post(test_url, json={"chat_id": TELEGRAM_CHANNEL_ID})
        data = response.json()
        
        if response.status_code == 200 and data.get("ok"):
            print("✅ <PERSON><PERSON> has access to the channel")
            return True
        else:
            print(f"❌ Bot access error: {data.get('description', 'Unknown error')}")
            return False
    except Exception as e:
        print(f"⚠️ Verification failed: {e}")
        return False

def get_download_link():
    """Get the current download link from the platform page"""
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(PLATFORM_URL, headers=headers, timeout=15)
        response.raise_for_status()

        # Look for the download link pattern
        # The download button should link to biozium.com
        pattern = r'href="(https://biozium\.com/public/bio-links/[^"]+)"'
        match = re.search(pattern, response.text)

        if match:
            download_url = match.group(1)
            print(f"📥 Found download link: {download_url}")
            return download_url
        else:
            print("⚠️ Could not find download link on platform page")
            return None
    except Exception as e:
        print(f"⚠️ Platform page error: {e}")
        return None

def extract_link():
    """Extract Canva Pro invite link by following the dynamic process"""
    try:
        # Step 1: Get the current download link from platform
        download_url = get_download_link()
        if not download_url:
            return None

        # Step 2: Follow the download link to get Canva invite
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(download_url, headers=headers, timeout=15)
        response.raise_for_status()

        # Search for the Canva invite link pattern
        pattern = r'const link = "(https://www\.canva\.com/brand/join\?[^"]+)"'
        match = re.search(pattern, response.text)

        if match:
            canva_link = match.group(1)
            print(f"🎯 Found Canva link: {canva_link}")
            return canva_link
        else:
            print("⚠️ Could not find Canva link on download page")
            return None
    except Exception as e:
        print(f"⚠️ Extraction error: {e}")
        return None

def send_to_channel(message, link=None):
    """Send message to Telegram channel with button and return status"""
    try:
        # Create inline keyboard if link is provided
        reply_markup = None
        if link:
            reply_markup = {
                "inline_keyboard": [
                    [{"text": "🎁 Claim Canva Pro Now", "url": link}]
                ]
            }
        
        # Send message to channel
        response = requests.post(
            f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage",
            json={
                "chat_id": TELEGRAM_CHANNEL_ID,
                "text": message,
                "parse_mode": "Markdown",
                "reply_markup": reply_markup
            },
            timeout=10
        )
        
        # Check response
        if response.status_code == 200:
            print("✅ Message sent successfully")
            return True
        else:
            error_msg = response.json().get('description', 'Unknown error')
            print(f"❌ Telegram API error: {error_msg}")
            return False
    except Exception as e:
        print(f"⚠️ Telegram error: {e}")
        return False

def main():
    print(f"🚀 Starting Canva Pro Monitor for your private channel")
    print(f"🔗 Monitoring: {PLATFORM_URL}")
    print(f"🔔 Updates will be sent to Channel ID: {TELEGRAM_CHANNEL_ID}")
    print(f"⏱ Checking every {CHECK_INTERVAL//60} minutes\n")
    
    # Verify bot access before proceeding
    if not verify_bot_access():
        print("\n❌ CRITICAL: Bot doesn't have access to the channel")
        print("Please make sure:")
        print("1. Bot @myProCanvaBot is added as admin to your channel")
        print("2. Bot has 'Post Messages' permission")
        print("3. Channel ID is correct")
        print("\nExiting...")
        return
    
    # First-run notification
    first_run = not os.path.exists(LINK_FILE)
    if first_run:
        print("🌟 First run detected")
        if send_to_channel("🔔 *Canva Pro Link Monitor Activated!*\n"
                          "I'll automatically post new invite links here."):
            print("✅ Channel activation message sent")
        else:
            print("❌ Failed to send activation message")
    
    while True:
        current_time = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"\n[{current_time}] Checking for updates...")
        
        current_link = extract_link()
        
        if current_link:
            print(f"🔗 Extracted link: {current_link}")
            
            # Check if link is new
            if first_run or not os.path.exists(LINK_FILE):
                # First link detected
                with open(LINK_FILE, "w") as f:
                    f.write(current_link)
                if send_to_channel(
                    f"🚀 *First Canva Pro Invite Detected!*\n\n"
                    f"⏰ Detected: {current_time}\n\n"
                    "_Click below to claim your access:_",
                    current_link
                ):
                    print("✅ Initial link sent to channel")
                    first_run = False
                else:
                    print("❌ Failed to send initial link")
                
            else:
                # Check if link has changed
                with open(LINK_FILE, "r") as f:
                    old_link = f.read().strip()
                
                if old_link != current_link:
                    # Update stored link
                    with open(LINK_FILE, "w") as f:
                        f.write(current_link)
                    
                    # Send to channel
                    if send_to_channel(
                        f"🔄 *New Canva Pro Invite Available!*\n\n"
                        f"⏰ Detected: {current_time}\n\n"
                        "_Click below to claim your access:_",
                        current_link
                    ):
                        print("✅ New link sent to channel")
                    else:
                        print("⚠️ Failed to send new link to channel")
                else:
                    print("✅ Link unchanged")
        else:
            print("⚠️ Could not extract link")
        
        # Wait for next check
        print(f"⏳ Next check in {CHECK_INTERVAL//60} minutes...")
        time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    # Run continuously with restart on error
    while True:
        try:
            main()
        except Exception as e:
            print(f"🛑 Critical error: {e}")
            print("🔄 Restarting in 60 seconds...")
            time.sleep(60)