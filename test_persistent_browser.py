import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

class TestPersistentBrowser:
    """Test the persistent browser functionality"""
    
    def __init__(self):
        self.driver = None
        self.is_initialized = False
        
    def initialize(self):
        """Initialize the test browser"""
        try:
            print("🤖 Initializing test persistent browser...")
            
            chrome_options = Options()
            # Keep browser visible for testing (remove --headless to see it work)
            # chrome_options.add_argument("--headless")  # Uncomment to hide browser
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            
            # Anti-detection measures
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Human-like settings
            prefs = {
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 1
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # Initialize driver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Remove webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Set timeouts
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)
            
            self.is_initialized = True
            
            print("✅ Test persistent browser initialized successfully")
            return True
            
        except Exception as e:
            print(f"⚠️ Failed to initialize test browser: {e}")
            return False
    
    def test_human_like_interaction(self, download_url):
        """Test human-like interaction with the page"""
        if not self.is_initialized:
            if not self.initialize():
                return None
        
        try:
            print(f"🤖 Testing human-like interaction...")
            print(f"🔄 Navigating to: {download_url}")
            
            # Navigate to the page
            self.driver.get(download_url)
            
            # Human-like behavior: wait and scroll around
            print("🤖 Performing human-like browsing behavior...")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/3);")
            time.sleep(1)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(1)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # Get initial state
            initial_content = self.driver.page_source
            initial_links = re.findall(r'(https://www\.canva\.com/[^"\s]+)', initial_content)
            print(f"🔍 Initial Canva links found: {len(initial_links)}")
            for i, link in enumerate(initial_links, 1):
                print(f"   {i}. {link}")
            
            # Look for GET HERE button
            get_here_selectors = [
                "//a[contains(text(), 'GET HERE')]",
                "//button[contains(text(), 'GET HERE')]",
                "//div[contains(text(), 'GET HERE')]",
                "//span[contains(text(), 'GET HERE')]",
                "//*[contains(@class, 'get-here')]",
                "//*[contains(@id, 'get-here')]"
            ]
            
            button_found = False
            for selector in get_here_selectors:
                try:
                    print(f"🔍 Looking for GET HERE button: {selector}")
                    button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    
                    print(f"✅ Found GET HERE button!")
                    
                    # Very human-like interaction
                    actions = ActionChains(self.driver)
                    
                    # Move to button slowly
                    print("🤖 Moving mouse to button (human-like)...")
                    actions.move_to_element(button).perform()
                    time.sleep(0.8)  # Human-like pause
                    
                    # Hover for a moment (like humans do)
                    print("🤖 Hovering over button...")
                    time.sleep(0.5)
                    
                    # Click with human-like timing
                    print("👆 Performing human-like click...")
                    actions.click_and_hold(button).perform()
                    time.sleep(0.15)  # Human click duration
                    actions.release().perform()
                    
                    print(f"✅ Human-like click completed!")
                    button_found = True
                    break
                    
                except (TimeoutException, NoSuchElementException):
                    continue
            
            if not button_found:
                print("⚠️ GET HERE button not found")
                return None
            
            # Wait and monitor for changes like a human would
            print(f"⏳ Waiting for page to update (monitoring like a human)...")
            
            for attempt in range(20):  # Wait up to 20 seconds
                time.sleep(1)
                
                # Check for redirect
                current_url = self.driver.current_url
                if 'canva.com' in current_url and current_url != download_url:
                    print(f"🎯 SUCCESS! Redirected to Canva: {current_url}")
                    return current_url
                
                # Check for updated content
                current_content = self.driver.page_source
                current_links = re.findall(r'(https://www\.canva\.com/[^"\s]+)', current_content)
                
                # Look for new links
                for link in current_links:
                    if link not in initial_links:
                        print(f"🎯 SUCCESS! Found NEW Canva link: {link}")
                        
                        # Compare with expected manual result
                        manual_link = "https://www.canva.com/signup/?brandAccessToken=uVfLa2sYKlBgWmKHEN0nAA&invitationToken=uVfLa2sYKlBgWmKHEN0nAA&brandingVariant=edu&signupRedirect=%2Fbrand%2Fjoin%3Ftoken%3DuVfLa2sYKlBgWmKHEN0nAA%26brandingVariant%3Dedu%26referrer%3Dteam-invite&loginRedirect=%2Fbrand%2Fjoin%3Ftoken%3DuVfLa2sYKlBgWmKHEN0nAA%26brandingVariant%3Dedu%26referrer%3Dteam-invite"
                        
                        print(f"\n🔍 Comparison with manual result:")
                        print(f"Browser found: {link}")
                        print(f"Manual result: {manual_link}")
                        
                        if link == manual_link:
                            print("🎉 PERFECT MATCH! Persistent browser is working perfectly!")
                        else:
                            # Compare tokens
                            browser_token = re.search(r'(?:brandAccessToken|token)=([^&]+)', link)
                            manual_token = re.search(r'brandAccessToken=([^&]+)', manual_link)
                            
                            if browser_token and manual_token:
                                print(f"Browser token: {browser_token.group(1)}")
                                print(f"Manual token:  {manual_token.group(1)}")
                                
                                if browser_token.group(1) == manual_token.group(1):
                                    print("✅ Same token - persistent browser is working!")
                                else:
                                    print("🔄 Different token - link has been updated!")
                        
                        return link
                    elif '/signup?' in link and 'brandAccessToken=' in link:
                        # Prefer signup URLs with brandAccessToken
                        print(f"🎯 Found fresh signup link: {link}")
                        return link
                
                # Human-like behavior: occasionally scroll or move mouse
                if attempt % 5 == 0:
                    self.driver.execute_script("window.scrollTo(0, Math.random() * 200);")
                    time.sleep(0.5)
                    self.driver.execute_script("window.scrollTo(0, 0);")
                
                if attempt % 3 == 0:
                    print(f"⏳ Still monitoring... {attempt + 1}/20")
            
            # Final check - return best available link
            final_content = self.driver.page_source
            final_links = re.findall(r'(https://www\.canva\.com/[^"\s]+)', final_content)
            
            print(f"🔍 Final check - found {len(final_links)} Canva links:")
            for i, link in enumerate(final_links, 1):
                print(f"   {i}. {link}")
            
            if final_links:
                # Prefer signup URLs
                signup_links = [link for link in final_links if '/signup?' in link]
                if signup_links:
                    best_link = signup_links[0]
                    print(f"🔍 Returning best signup link: {best_link}")
                else:
                    best_link = final_links[0]
                    print(f"🔍 Returning best available link: {best_link}")
                
                return best_link
            
            return None
            
        except Exception as e:
            print(f"⚠️ Test error: {e}")
            return None
    
    def cleanup(self):
        """Clean up the test browser"""
        if self.driver:
            try:
                print("🔒 Closing test browser...")
                self.driver.quit()
            except:
                pass
        self.driver = None
        self.is_initialized = False

# Test the persistent browser
if __name__ == "__main__":
    download_url = "https://biozium.com/public/bio-links/kane-tanner/"
    
    print("=== Testing Persistent Browser ===")
    print(f"Target URL: {download_url}")
    print("Note: Browser window will be visible for testing")
    
    test_browser = TestPersistentBrowser()
    
    try:
        result = test_browser.test_human_like_interaction(download_url)
        
        if result:
            print(f"\n✅ SUCCESS! Persistent browser found: {result}")
        else:
            print(f"\n❌ Persistent browser test failed")
    
    finally:
        test_browser.cleanup()
        print("🔒 Test completed")
